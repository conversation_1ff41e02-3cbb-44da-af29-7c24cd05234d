apiVersion: v1
kind: Secret
metadata:
  name: ai-react-frontend-secrets
  labels:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/version: 738a427a
    app.kubernetes.io/managed-by: argocd
type: Opaque
data:
  # Dynamic secrets extracted from GitHub Actions secrets JSON payload
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  # Essential Authentication Secrets
  JWT_SECRET: DYNAMIC_JWT_SECRET_B64

  # Database Credentials
  DB_USER: DYNAMIC_DB_USER_B64
  DB_PASSWORD: DYNAMIC_DB_PASSWORD_B64
  DB_HOST: DYNAMIC_DB_HOST_B64
  DB_PORT: DYNAMIC_DB_PORT_B64
  DB_NAME: DYNAMIC_DB_NAME_B64

  # SMTP Configuration
  SMTP_USER: DYNAMIC_SMTP_USER_B64
  SMTP_PASS: DYNAMIC_SMTP_PASS_B64

  # OAuth2 Configuration
  GOOGLE_CLIENT_ID: DYNAMIC_GOOGLE_CLIENT_ID_B64
  GOOGLE_CLIENT_SECRET: DYNAMIC_GOOGLE_CLIENT_SECRET_B64

  # Database SSL Mode (fixed value)
  DB_SSL_MODE: DYNAMIC_DB_SSL_MODE_B64
