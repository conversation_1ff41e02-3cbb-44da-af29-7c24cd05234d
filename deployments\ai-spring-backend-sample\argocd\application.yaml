apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-spring-backend-sample-staging
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-spring-backend-sample-staging
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: 7d77a0f6
    app.kubernetes.io/managed-by: argocd
    environment: staging
    app-type: springboot-backend
    source.repo: ChidhagniConsulting-ai-spring-backend
    source.branch: 25-merge
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-spring-backend-sample-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-spring-backend-sample/overlays/staging
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-spring-backend-sample-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Staging environment for ai-spring-backend-sample"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting-ai-spring-backend"
  - name: Environment
    value: "staging"
  - name: Application Type
    value: "springboot-backend"
  - name: Source Branch
    value: "25-merge"
  - name: Commit SHA
    value: "7d77a0f6"
  - name: Configuration
    value: "Staging configuration with production-like settings"
