#!/usr/bin/env python3
"""
Template Fixes Validation Script
Tests that the template-level fixes are working correctly
"""

import os
import subprocess
import sys
import tempfile
import yaml
from pathlib import Path


def print_status(message: str, status_type: str = "INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def run_command(command: str, cwd: str = None) -> tuple:
    """Run a command and return (success, output, error)"""
    try:
        result = subprocess.run(
            command.split(),
            cwd=cwd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            check=False
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)


def test_kustomize_build(overlay_path: str) -> bool:
    """Test that kustomize can build the overlay without errors"""
    print_status(f"Testing kustomize build for {overlay_path}...", "INFO")
    
    success, output, error = run_command(f"kubectl kustomize {overlay_path}")
    
    if success:
        print_status(f"✅ Kustomize build successful for {overlay_path}", "SUCCESS")
        return True
    else:
        print_status(f"❌ Kustomize build failed for {overlay_path}", "ERROR")
        print_status(f"Error: {error}", "ERROR")
        return False


def test_init_container_config(overlay_path: str) -> bool:
    """Test that the init container configuration is correct"""
    print_status(f"Testing init container configuration for {overlay_path}...", "INFO")
    
    success, output, error = run_command(f"kubectl kustomize {overlay_path}")
    
    if not success:
        print_status(f"❌ Failed to build manifests for {overlay_path}", "ERROR")
        return False
    
    # Parse the YAML output to check init container configuration
    try:
        if not output.strip():
            print_status("⚠️ No output from kustomize build", "WARNING")
            return True

        documents = list(yaml.safe_load_all(output))
        deployment_found = False
        init_container_correct = False
        
        for doc in documents:
            if doc and doc.get('kind') == 'Deployment':
                deployment_found = True
                init_containers = doc.get('spec', {}).get('template', {}).get('spec', {}).get('initContainers', [])
                
                for init_container in init_containers:
                    if init_container.get('name') == 'wait-for-database':
                        env_vars = init_container.get('env', [])
                        
                        # Check that we're using direct environment variables (not base64 encoded)
                        db_host_found = False
                        for env_var in env_vars:
                            if env_var.get('name') == 'DB_HOST':
                                db_host_found = True
                                break
                        
                        if db_host_found:
                            init_container_correct = True
                            print_status("✅ Init container uses direct environment variables", "SUCCESS")
                        else:
                            print_status("❌ Init container still uses base64 encoded variables", "ERROR")
                        break
        
        if not deployment_found:
            print_status("⚠️ No deployment found in manifests", "WARNING")
            return True  # This might be expected for some overlays
        
        return init_container_correct
        
    except yaml.YAMLError as e:
        print_status(f"❌ Failed to parse YAML output: {e}", "ERROR")
        return False


def test_environment_isolation() -> bool:
    """Test that environment-specific processing works correctly"""
    print_status("Testing environment isolation...", "INFO")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Copy manifests to temp directory
        manifests_src = Path("manifests")
        manifests_dst = temp_path / "manifests"
        
        success, _, error = run_command(f"cp -r {manifests_src} {manifests_dst}")
        if not success:
            print_status(f"❌ Failed to copy manifests: {error}", "ERROR")
            return False
        
        # Test that each environment overlay can be built independently
        environments = ["dev", "staging", "production"]
        all_success = True
        
        for env in environments:
            overlay_path = manifests_dst / "overlays" / env
            if overlay_path.exists():
                env_success = test_kustomize_build(str(overlay_path))
                all_success = all_success and env_success
            else:
                print_status(f"⚠️ Environment overlay not found: {env}", "WARNING")
        
        return all_success


def test_template_consistency() -> bool:
    """Test that template files are consistent and don't have conflicting configurations"""
    print_status("Testing template consistency...", "INFO")
    
    # Check that the database-init component doesn't have base64 decoding
    init_patch_file = Path("manifests/components/database-init/init-container-patch.yaml")
    
    if not init_patch_file.exists():
        print_status("❌ Database init patch file not found", "ERROR")
        return False
    
    try:
        with open(init_patch_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for problematic patterns
        if "base64 -d" in content:
            print_status("❌ Template still contains base64 decoding", "ERROR")
            return False
        
        if "DB_HOST_B64" in content:
            print_status("❌ Template still uses base64 environment variable names", "ERROR")
            return False
        
        if "DB_HOST" in content and "valueFrom" in content:
            print_status("✅ Template uses correct direct environment variables", "SUCCESS")
            return True
        else:
            print_status("❌ Template doesn't have expected environment variable configuration", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"❌ Failed to read template file: {e}", "ERROR")
        return False


def main():
    print_status("🧪 Testing GitOps Template Fixes", "INFO")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: Template consistency
    print_status("\n1. Testing template consistency...", "INFO")
    test1_passed = test_template_consistency()
    all_tests_passed = all_tests_passed and test1_passed
    
    # Test 2: Environment isolation
    print_status("\n2. Testing environment isolation...", "INFO")
    test2_passed = test_environment_isolation()
    all_tests_passed = all_tests_passed and test2_passed
    
    # Test 3: Init container configuration for each environment
    print_status("\n3. Testing init container configuration...", "INFO")
    environments = ["dev", "staging", "production"]
    test3_passed = True
    
    for env in environments:
        overlay_path = f"manifests/overlays/{env}"
        if Path(overlay_path).exists():
            env_test_passed = test_init_container_config(overlay_path)
            test3_passed = test3_passed and env_test_passed
    
    all_tests_passed = all_tests_passed and test3_passed
    
    # Summary
    print_status("\n📋 TEST SUMMARY", "INFO")
    print("=" * 60)
    
    if all_tests_passed:
        print_status("✅ All tests passed! Template fixes are working correctly.", "SUCCESS")
        print_status("The GitOps pipeline is ready for production use.", "SUCCESS")
        return 0
    else:
        print_status("❌ Some tests failed. Please review the issues above.", "ERROR")
        print_status("Fix the issues before deploying to production.", "ERROR")
        return 1


if __name__ == "__main__":
    sys.exit(main())
