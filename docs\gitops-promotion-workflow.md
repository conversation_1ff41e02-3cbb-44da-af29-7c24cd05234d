# GitOps Promotion Workflow

This document explains the automated GitOps promotion workflow implemented in `.github/workflows/deploy-from-cicd.yaml` that automatically promotes applications between environments with environment-specific Docker tags.

## Overview

The promotion workflow automatically triggers promotions between environments after successful deployments:

- **dev → staging**: After successful dev deployment, manual approval required
- **staging → production**: After successful staging deployment, manual approval required

Each environment uses specific Docker tags:
- `dev` environment: `latest` tag
- `staging` environment: `staging` tag  
- `production` environment: `production` tag

## Workflow Architecture

```mermaid
graph TD
    A[CI/CD Pipeline] --> B[Deploy to Dev]
    B --> C{Dev Deployment Success?}
    C -->|Yes| D[Promote to Staging Job]
    C -->|No| E[End]
    D --> F[Manual Approval Required]
    F --> G[Deploy to Staging]
    G --> H{Staging Deployment Success?}
    H -->|Yes| I[Promote to Production Job]
    H -->|No| E
    I --> J[Manual Approval Required]
    J --> K[Deploy to Production]
    K --> E
```

## Environment-Specific Docker Tag Mapping

The workflow automatically maps environments to their corresponding Docker tags:

| Environment | Docker Tag | Description |
|-------------|------------|-------------|
| `dev` | `latest` | Development builds use the latest tag |
| `staging` | `staging` | Staging builds use the staging tag |
| `production` | `production` | Production builds use the production tag |

## GitHub Environment Setup

To enable manual approvals, you need to configure GitHub environments:

### 1. Create Staging Approval Environment

1. Go to your repository → Settings → Environments
2. Click "New environment"
3. Name: `staging-approval`
4. Configure protection rules:
   - ✅ Required reviewers (add team members who can approve staging deployments)
   - ✅ Wait timer: 0 minutes (optional)
   - ✅ Prevent administrators from bypassing configured protection rules (recommended)

### 2. Create Production Approval Environment

1. Go to your repository → Settings → Environments
2. Click "New environment"
3. Name: `production-approval`
4. Configure protection rules:
   - ✅ Required reviewers (add team members who can approve production deployments)
   - ✅ Wait timer: 0 minutes (optional)
   - ✅ Prevent administrators from bypassing configured protection rules (recommended)

## How the Promotion Workflow Works

### 1. Initial Deployment

When a CI/CD pipeline triggers a deployment to `dev`:

```json
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "my-app",
    "project_id": "my-project",
    "environment": "dev",
    "docker_image": "registry.example.com/my-app",
    "docker_tag": "latest",
    // ... other parameters
  }
}
```

### 2. Automatic Staging Promotion

After successful dev deployment:

1. The `promote-to-staging` job is triggered
2. It waits for manual approval via the `staging-approval` environment
3. Once approved, it creates a new payload with:
   - `environment`: `"staging"`
   - `docker_tag`: `"staging"`
   - All other parameters preserved from original deployment
4. Triggers a new deployment via repository dispatch

### 3. Automatic Production Promotion

After successful staging deployment:

1. The `promote-to-production` job is triggered
2. It waits for manual approval via the `production-approval` environment
3. Once approved, it creates a new payload with:
   - `environment`: `"production"`
   - `docker_tag`: `"production"`
   - All other parameters preserved from original deployment
4. Triggers a new deployment via repository dispatch

## Approval Process

### Approving Staging Promotion

1. Go to GitHub Actions → Your workflow run
2. Look for the "promote-to-staging" job
3. Click "Review deployments"
4. Select "staging-approval" environment
5. Add optional comment and click "Approve and deploy"

### Approving Production Promotion

1. Go to GitHub Actions → Your workflow run
2. Look for the "promote-to-production" job
3. Click "Review deployments"
4. Select "production-approval" environment
5. Add optional comment and click "Approve and deploy"

## Monitoring and Troubleshooting

### Viewing Promotion Status

The workflow includes a comprehensive summary job that shows:
- Original deployment details
- Promotion status for each environment
- Next steps and available actions
- Environment-specific Docker tag information

### Common Issues

1. **Missing GitHub Environments**: Ensure `staging-approval` and `production-approval` environments are configured
2. **Insufficient Permissions**: Verify that the `GITOPS_TOKEN` has repository dispatch permissions
3. **Failed Promotions**: Check the workflow logs for detailed error information

### Manual Promotion

If automatic promotion fails, you can use the existing manual promotion workflow:

```bash
# Using the promote-environment workflow
gh workflow run promote-environment.yaml \
  --field project_id=my-project \
  --field source_environment=dev \
  --field target_environment=staging
```

## Integration with Existing Workflows

This promotion workflow integrates seamlessly with:

- Existing ArgoCD deployment logic
- Current manifest generation system
- Environment-specific configurations
- Kustomize overlay structure
- Existing validation and error handling

All deployment parameters (app_name, project_id, application_type, etc.) are preserved during promotion, ensuring consistency across environments.
