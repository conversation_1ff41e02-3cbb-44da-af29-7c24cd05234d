apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-spring-backend-sample
  labels:
    app: ai-spring-backend-sample
    app.kubernetes.io/name: ai-spring-backend-sample
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/version: 7d77a0f6
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-spring-backend-sample
  template:
    metadata:
      labels:
        app: ai-spring-backend-sample
        app.kubernetes.io/name: ai-spring-backend-sample
        app.kubernetes.io/component: springboot-backend
        app.kubernetes.io/part-of: ai-spring-backend-sample
        app.kubernetes.io/version: 7d77a0f6
    spec:
      containers:
      - name: ai-spring-backend-sample
        image: registry.digitalocean.com/doks-registry/ai-spring-backend:staging
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: ai-spring-backend-sample-config
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: JWT_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_NAME
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: GOOGLE_CLIENT_SECRET
        livenessProbe:
          tcpSocket:
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
