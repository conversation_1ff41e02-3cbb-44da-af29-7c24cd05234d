#!/usr/bin/env python3
"""
GitHub Environment Approval Gates Verification Script
Verifies that manual approval gates are properly configured for GitOps deployments
"""

import argparse
import json
import os
import sys
from typing import Dict, List, Optional

try:
    import requests
except ImportError:
    print("❌ requests module not available. Install with: pip install requests")
    print("Continuing with basic workflow verification only...")
    requests = None


def print_status(message: str, status_type: str = "INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def get_github_token() -> Optional[str]:
    """Get GitHub token from environment or command line"""
    token = os.getenv('GITHUB_TOKEN') or os.getenv('GITOPS_TOKEN')
    if not token:
        print_status("GitHub token not found. Set GITHUB_TOKEN or GITOPS_TOKEN environment variable", "ERROR")
        return None
    return token


def check_environment_exists(repo: str, environment: str, token: str) -> Dict:
    """Check if a GitHub environment exists and get its configuration"""
    if not requests:
        return {"error": "requests module not available"}

    url = f"https://api.github.com/repos/{repo}/environments/{environment}"
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 404:
            return {"error": "Environment not found"}
        else:
            return {"error": f"API error: {response.status_code} - {response.text}"}
    except Exception as e:
        return {"error": f"Request failed: {e}"}


def check_environment_protection_rules(repo: str, environment: str, token: str) -> Dict:
    """Check environment protection rules"""
    url = f"https://api.github.com/repos/{repo}/environments/{environment}/deployment-protection-rules"
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3+json'
    }
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"API error: {response.status_code} - {response.text}"}
    except Exception as e:
        return {"error": f"Request failed: {e}"}


def verify_workflow_configuration(workflow_file: str) -> Dict:
    """Verify workflow file has correct environment configurations"""
    try:
        # Try different encodings
        content = None
        for encoding in ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']:
            try:
                with open(workflow_file, 'r', encoding=encoding) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            return {"error": "Could not decode workflow file with any common encoding"}

        results = {
            "staging_approval_found": "environment: staging-approval" in content,
            "production_approval_found": "environment: production-approval" in content,
            "staging_job_conditional": "needs.deploy-to-argocd.outputs.environment == 'dev'" in content,
            "production_job_conditional": "needs.deploy-to-argocd.outputs.environment == 'staging'" in content
        }

        return results
    except Exception as e:
        return {"error": f"Failed to read workflow file: {e}"}


def main():
    parser = argparse.ArgumentParser(description="Verify GitHub environment approval gates")
    parser.add_argument("--repo", default="ChidhagniConsulting/gitops-argocd-apps", 
                       help="GitHub repository (owner/repo)")
    parser.add_argument("--token", help="GitHub token (or use GITHUB_TOKEN env var)")
    parser.add_argument("--workflow-file", default=".github/workflows/deploy-from-cicd.yaml",
                       help="Path to workflow file")
    
    args = parser.parse_args()
    
    # Get GitHub token (optional for workflow verification)
    token = args.token or get_github_token()
    if not token:
        print_status("No GitHub token provided - will only verify workflow configuration", "WARNING")
    
    print_status("🔍 Verifying GitHub Environment Approval Gates", "INFO")
    print_status(f"Repository: {args.repo}", "INFO")
    print()
    
    # Check workflow configuration
    print_status("1. Checking workflow configuration...", "INFO")
    workflow_config = verify_workflow_configuration(args.workflow_file)
    
    if "error" in workflow_config:
        print_status(f"Workflow check failed: {workflow_config['error']}", "ERROR")
    else:
        for check, result in workflow_config.items():
            status = "SUCCESS" if result else "ERROR"
            print_status(f"   {check}: {result}", status)
    
    print()
    
    # Check environments only if token is available
    staging_env = {"error": "No token provided"}
    production_env = {"error": "No token provided"}

    if token:
        # Check staging-approval environment
        print_status("2. Checking staging-approval environment...", "INFO")
        staging_env = check_environment_exists(args.repo, "staging-approval", token)

        if "error" in staging_env:
            print_status(f"   staging-approval: {staging_env['error']}", "ERROR")
            print_status("   ❌ staging-approval environment not configured", "ERROR")
        else:
            print_status("   ✅ staging-approval environment exists", "SUCCESS")
            if 'protection_rules' in staging_env:
                rules = staging_env['protection_rules']
                print_status(f"   Required reviewers: {len(rules.get('reviewers', []))}", "INFO")
                print_status(f"   Wait timer: {rules.get('wait_timer', 0)} minutes", "INFO")

        print()

        # Check production-approval environment
        print_status("3. Checking production-approval environment...", "INFO")
        production_env = check_environment_exists(args.repo, "production-approval", token)

        if "error" in production_env:
            print_status(f"   production-approval: {production_env['error']}", "ERROR")
            print_status("   ❌ production-approval environment not configured", "ERROR")
        else:
            print_status("   ✅ production-approval environment exists", "SUCCESS")
            if 'protection_rules' in production_env:
                rules = production_env['protection_rules']
                print_status(f"   Required reviewers: {len(rules.get('reviewers', []))}", "INFO")
                print_status(f"   Wait timer: {rules.get('wait_timer', 0)} minutes", "INFO")
    else:
        print_status("2. Skipping environment checks (no GitHub token)", "WARNING")
        print_status("   To check environments, provide GitHub token via --token or GITHUB_TOKEN env var", "INFO")
    
    print()
    
    # Summary
    print_status("📋 VERIFICATION SUMMARY", "INFO")
    print("=" * 50)
    
    workflow_ok = all(workflow_config.get(k, False) for k in [
        'staging_approval_found', 'production_approval_found', 
        'staging_job_conditional', 'production_job_conditional'
    ]) if "error" not in workflow_config else False
    
    staging_ok = "error" not in staging_env
    production_ok = "error" not in production_env
    
    if workflow_ok and staging_ok and production_ok:
        print_status("✅ All approval gates are properly configured!", "SUCCESS")
        print_status("Manual approval will be required for staging and production deployments", "SUCCESS")
    else:
        print_status("❌ Approval gates configuration has issues:", "ERROR")
        if not workflow_ok:
            print_status("   - Workflow configuration needs fixes", "ERROR")
        if not staging_ok:
            print_status("   - staging-approval environment needs setup", "ERROR")
        if not production_ok:
            print_status("   - production-approval environment needs setup", "ERROR")
        
        print()
        print_status("🔧 REQUIRED ACTIONS:", "WARNING")
        print("1. Go to GitHub repository Settings → Environments")
        print("2. Create missing environments with protection rules")
        print("3. Add required reviewers to each environment")
        print("4. Test the approval flow with a deployment")


if __name__ == "__main__":
    main()
