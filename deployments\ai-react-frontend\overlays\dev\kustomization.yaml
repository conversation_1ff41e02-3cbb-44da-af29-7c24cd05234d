apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: PLACEHOLDER_PROJECT_ID-dev

resources:
- ../../base

components:
- ../../components/common-labels
- ../../components/database-init

labels:
- pairs:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: PLACEHOLDER_SOURCE_REPO_LABEL
    source.branch: PLACEHOLDER_SOURCE_BRANCH_LABEL

patches:
- target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 1
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: "250m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: "256Mi"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/cpu
      value: "500m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: "512Mi"

- target:
    kind: ConfigMap
    name: PLACEHOLDER_PROJECT_ID-config
  patch: |-
    - op: replace
      path: /data/NODE_ENV
      value: "dev"
    - op: replace
      path: /data/SPRING_PROFILES_ACTIVE
      value: "dev"
    - op: replace
      path: /data/DEBUG
      value: "True"
    - op: replace
      path: /data/GENERATE_SOURCEMAP
      value: "true"

# Database init container environment-specific patch
- target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
  patch: |-
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: PLACEHOLDER_PROJECT_ID
    spec:
      template:
        spec:
          initContainers:
          - name: wait-for-database
            env:
            - name: ENVIRONMENT
              value: "dev"
            - name: TEST_CONNECTION
              value: "true"

patchesStrategicMerge:
- patch-image.yaml

namePrefix: ""
nameSuffix: "-dev"
