apiVersion: v1
kind: Service
metadata:
  name: ai-spring-backend-sample-service
  labels:
    app: ai-spring-backend-sample
    app.kubernetes.io/name: ai-spring-backend-sample
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/version: 7d77a0f6
    app.kubernetes.io/managed-by: argocd
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: ai-spring-backend-sample
