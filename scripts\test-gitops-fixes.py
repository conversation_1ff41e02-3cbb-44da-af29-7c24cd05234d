#!/usr/bin/env python3
"""
GitOps Deployment Pipeline Test Script
Tests the fixes for placeholder replacement, manual approval gates, and deployment flow sequence
"""

import argparse
import json
import os
import subprocess
import sys
import tempfile
from pathlib import Path


def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]",
        "TEST": "\033[35m[TEST]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def test_placeholder_replacement():
    """Test that placeholders are correctly replaced in manifests"""
    print_status("Testing placeholder replacement...", "TEST")
    
    # Create test payload
    test_payload = {
        "app_name": "Test Spring Backend",
        "project_id": "ai-spring-backend-sample",
        "application_type": "springboot-backend",
        "environment": "dev",
        "docker_image": "registry.digitalocean.com/doks-registry/ai-spring-backend",
        "docker_tag": "latest",
        "source_repo": "ChidhagniConsulting/ai-spring-backend",
        "source_branch": "main",
        "commit_sha": "abc123def456"
    }
    
    # Test the process_payload.py script
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Write payload to file
            payload_file = Path(temp_dir) / "test_payload.json"
            with open(payload_file, 'w') as f:
                json.dump(test_payload, f)
            
            # Run the process_payload.py script
            cmd = [
                sys.executable, "scripts/process_payload.py",
                "--payload-file", str(payload_file),
                "--environment", "dev",
                "--manifest-dir", "deployments/ai-spring-backend-sample",
                "--output-dir", temp_dir,
                "--validate"
            ]
            
            print_status(f"Running: {' '.join(cmd)}", "INFO")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print_status("✓ Placeholder replacement script executed successfully", "SUCCESS")
                
                # Check if processed files exist
                processed_dir = Path(temp_dir) / "ai-spring-backend-sample"
                if processed_dir.exists():
                    print_status("✓ Processed manifests directory created", "SUCCESS")
                    
                    # Check specific files for placeholder replacement
                    test_files = [
                        "overlays/dev/application.yaml",
                        "overlays/dev/kustomization.yaml", 
                        "overlays/dev/patch-image.yaml"
                    ]
                    
                    for test_file in test_files:
                        file_path = processed_dir / test_file
                        if file_path.exists():
                            with open(file_path, 'r') as f:
                                content = f.read()
                            
                            # Check if placeholders were replaced
                            if "PLACEHOLDER_" in content:
                                print_status(f"✗ Placeholders still found in {test_file}", "ERROR")
                                return False
                            else:
                                print_status(f"✓ Placeholders replaced in {test_file}", "SUCCESS")
                        else:
                            print_status(f"✗ Test file not found: {test_file}", "ERROR")
                            return False
                    
                    return True
                else:
                    print_status("✗ Processed manifests directory not created", "ERROR")
                    return False
            else:
                print_status(f"✗ Placeholder replacement failed: {result.stderr}", "ERROR")
                return False
                
    except Exception as e:
        print_status(f"✗ Placeholder replacement test failed: {e}", "ERROR")
        return False


def test_deployment_sequence_validation():
    """Test the deployment sequence validation"""
    print_status("Testing deployment sequence validation...", "TEST")
    
    try:
        # Test valid sequence (dev)
        cmd = [
            sys.executable, "scripts/validate-deployment-sequence.py",
            "--project-id", "ai-spring-backend-sample",
            "--environment", "dev"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_status("✓ Dev deployment sequence validation passed", "SUCCESS")
        else:
            print_status("✗ Dev deployment sequence validation failed", "ERROR")
            return False
        
        # Test staging sequence (should work if dev exists)
        cmd = [
            sys.executable, "scripts/validate-deployment-sequence.py",
            "--project-id", "ai-spring-backend-sample", 
            "--environment", "staging"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_status("✓ Staging deployment sequence validation passed", "SUCCESS")
        else:
            print_status("⚠ Staging deployment sequence validation had warnings (expected)", "WARNING")
        
        return True
        
    except Exception as e:
        print_status(f"✗ Deployment sequence validation test failed: {e}", "ERROR")
        return False


def test_manifest_structure():
    """Test that the manifest structure is correct"""
    print_status("Testing manifest structure...", "TEST")
    
    project_dir = Path("deployments/ai-spring-backend-sample")
    
    # Check required directories
    required_dirs = [
        "base",
        "overlays/dev",
        "overlays/staging", 
        "overlays/production",
        "argocd",
        "components"
    ]
    
    for dir_path in required_dirs:
        full_path = project_dir / dir_path
        if full_path.exists():
            print_status(f"✓ Directory exists: {dir_path}", "SUCCESS")
        else:
            print_status(f"✗ Directory missing: {dir_path}", "ERROR")
            return False
    
    # Check required files
    required_files = [
        "overlays/dev/application.yaml",
        "overlays/dev/kustomization.yaml",
        "overlays/dev/patch-image.yaml",
        "overlays/staging/application.yaml",
        "overlays/staging/kustomization.yaml", 
        "overlays/staging/patch-image.yaml",
        "argocd/project.yaml"
    ]
    
    for file_path in required_files:
        full_path = project_dir / file_path
        if full_path.exists():
            print_status(f"✓ File exists: {file_path}", "SUCCESS")
        else:
            print_status(f"✗ File missing: {file_path}", "ERROR")
            return False
    
    return True


def test_docker_tag_logic():
    """Test that Docker tag logic is correct"""
    print_status("Testing Docker tag logic...", "TEST")
    
    # Test environment-specific tags
    test_cases = [
        ("dev", "latest"),
        ("staging", "staging"),
        ("production", "production")
    ]
    
    for environment, expected_tag in test_cases:
        # Create test payload
        payload = {
            "environment": environment,
            "docker_tag": expected_tag
        }
        
        # Test with validation script
        try:
            cmd = [
                sys.executable, "scripts/validate-deployment-sequence.py",
                "--project-id", "ai-spring-backend-sample",
                "--environment", environment,
                "--payload", json.dumps(payload)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print_status(f"✓ Docker tag validation passed for {environment}: {expected_tag}", "SUCCESS")
            else:
                print_status(f"✗ Docker tag validation failed for {environment}: {expected_tag}", "ERROR")
                return False
                
        except Exception as e:
            print_status(f"✗ Docker tag test failed for {environment}: {e}", "ERROR")
            return False
    
    return True


def main():
    parser = argparse.ArgumentParser(description="Test GitOps deployment pipeline fixes")
    parser.add_argument("--test", choices=["all", "placeholders", "sequence", "structure", "tags"],
                       default="all", help="Which test to run")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    print("GitOps Deployment Pipeline Test Suite")
    print("=" * 50)
    
    # Change to repository root
    if not Path("deployments").exists():
        print_status("Please run from repository root directory", "ERROR")
        sys.exit(1)
    
    tests = []
    if args.test in ["all", "structure"]:
        tests.append(("Manifest Structure", test_manifest_structure))
    if args.test in ["all", "placeholders"]:
        tests.append(("Placeholder Replacement", test_placeholder_replacement))
    if args.test in ["all", "sequence"]:
        tests.append(("Deployment Sequence", test_deployment_sequence_validation))
    if args.test in ["all", "tags"]:
        tests.append(("Docker Tag Logic", test_docker_tag_logic))
    
    # Run tests
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print_status(f"✅ {test_name} PASSED", "SUCCESS")
                passed += 1
            else:
                print_status(f"❌ {test_name} FAILED", "ERROR")
                failed += 1
        except Exception as e:
            print_status(f"❌ {test_name} ERROR: {e}", "ERROR")
            failed += 1
    
    # Summary
    print(f"\n{'='*50}")
    print_status(f"Test Results: {passed} passed, {failed} failed", "INFO")
    
    if failed == 0:
        print_status("🎉 All tests passed! GitOps fixes are working correctly.", "SUCCESS")
        sys.exit(0)
    else:
        print_status("❌ Some tests failed. Please review the issues above.", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()
