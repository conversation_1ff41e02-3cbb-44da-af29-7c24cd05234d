#!/usr/bin/env python3
"""
Test script for GitOps promotion workflow
Validates the promotion logic and environment-specific Docker tag mapping
"""

import json
import sys
from typing import Dict, Any


def test_environment_tag_mapping():
    """Test environment-specific Docker tag mapping"""
    print("🧪 Testing environment-specific Docker tag mapping...")
    
    test_cases = [
        {"environment": "dev", "expected_tag": "latest"},
        {"environment": "staging", "expected_tag": "staging"},
        {"environment": "production", "expected_tag": "production"}
    ]
    
    for case in test_cases:
        env = case["environment"]
        expected = case["expected_tag"]
        
        # Simulate the tag mapping logic from the workflow
        if env == "dev":
            actual_tag = "latest"
        elif env == "staging":
            actual_tag = "staging"
        elif env == "production":
            actual_tag = "production"
        else:
            actual_tag = "unknown"
        
        if actual_tag == expected:
            print(f"  ✅ {env} → {actual_tag}")
        else:
            print(f"  ❌ {env} → {actual_tag} (expected {expected})")
            return False
    
    return True


def test_promotion_payload_generation():
    """Test promotion payload generation"""
    print("\n🧪 Testing promotion payload generation...")
    
    # Original dev deployment payload
    original_payload = {
        "app_name": "test-app",
        "project_id": "test-project",
        "application_type": "web-app",
        "environment": "dev",
        "docker_image": "registry.example.com/test-app",
        "docker_tag": "latest",
        "source_repo": "example/test-app",
        "source_branch": "main",
        "commit_sha": "abc123",
        "secrets_encoded": "base64encodedstring"
    }
    
    # Test dev → staging promotion
    staging_payload = create_promotion_payload(original_payload, "staging")
    
    # Validate staging payload
    expected_changes = {
        "environment": "staging",
        "docker_tag": "staging"
    }
    
    success = True
    for key, expected_value in expected_changes.items():
        if staging_payload.get(key) != expected_value:
            print(f"  ❌ Staging promotion: {key} = {staging_payload.get(key)} (expected {expected_value})")
            success = False
        else:
            print(f"  ✅ Staging promotion: {key} = {staging_payload.get(key)}")
    
    # Validate that other fields are preserved
    preserved_fields = ["app_name", "project_id", "application_type", "docker_image", 
                       "container_port", "health_check_path", "backend_type", 
                       "source_repo", "source_branch", "commit_sha", "secrets_encoded"]
    
    for field in preserved_fields:
        if staging_payload.get(field) != original_payload.get(field):
            print(f"  ❌ Field not preserved: {field}")
            success = False
    
    if success:
        print("  ✅ All fields preserved correctly")
    
    # Test staging → production promotion
    production_payload = create_promotion_payload(staging_payload, "production")
    
    if production_payload.get("environment") == "production" and production_payload.get("docker_tag") == "production":
        print("  ✅ Production promotion: environment and docker_tag updated correctly")
    else:
        print("  ❌ Production promotion: environment or docker_tag not updated correctly")
        success = False
    
    return success


def create_promotion_payload(original_payload: Dict[str, Any], target_environment: str) -> Dict[str, Any]:
    """Create promotion payload with environment-specific Docker tag"""
    promotion_payload = original_payload.copy()
    
    # Update environment
    promotion_payload["environment"] = target_environment
    
    # Update Docker tag based on target environment
    if target_environment == "dev":
        promotion_payload["docker_tag"] = "latest"
    elif target_environment == "staging":
        promotion_payload["docker_tag"] = "staging"
    elif target_environment == "production":
        promotion_payload["docker_tag"] = "production"
    
    return promotion_payload


def test_promotion_paths():
    """Test valid promotion paths"""
    print("\n🧪 Testing promotion paths...")
    
    valid_paths = [
        ("dev", "staging"),
        ("staging", "production")
    ]
    
    invalid_paths = [
        ("dev", "production"),  # Should go through staging first
        ("staging", "dev"),     # Backward promotion not allowed
        ("production", "staging"),  # Backward promotion not allowed
        ("production", "dev")   # Backward promotion not allowed
    ]
    
    success = True
    
    for source, target in valid_paths:
        if is_valid_promotion_path(source, target):
            print(f"  ✅ {source} → {target} (valid)")
        else:
            print(f"  ❌ {source} → {target} (should be valid)")
            success = False
    
    for source, target in invalid_paths:
        if not is_valid_promotion_path(source, target):
            print(f"  ✅ {source} → {target} (correctly rejected)")
        else:
            print(f"  ❌ {source} → {target} (should be rejected)")
            success = False
    
    return success


def is_valid_promotion_path(source_env: str, target_env: str) -> bool:
    """Validate promotion path"""
    allowed_promotions = {
        'dev': ['staging'],
        'staging': ['production'],
        'production': []  # No promotions from production
    }
    
    return target_env in allowed_promotions.get(source_env, [])


def main():
    """Run all tests"""
    print("🚀 GitOps Promotion Workflow Test Suite")
    print("=" * 50)
    
    tests = [
        ("Environment Tag Mapping", test_environment_tag_mapping),
        ("Promotion Payload Generation", test_promotion_payload_generation),
        ("Promotion Paths", test_promotion_paths)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        if test_func():
            print(f"✅ {test_name} PASSED")
            passed += 1
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The promotion workflow is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
